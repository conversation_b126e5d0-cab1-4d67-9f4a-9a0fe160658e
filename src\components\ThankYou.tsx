import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import gsap from 'gsap';
import Button from './Button';
import './ThankYou.css';

interface ThankYouState {
  userName: string;
}

export default function ThankYou() {
  const location = useLocation();
  const navigate = useNavigate();
  const state = location.state as ThankYouState;
  
  // If no user name is provided, redirect to home
  useEffect(() => {
    if (!state?.userName) {
      navigate('/');
      return;
    }
  }, [state, navigate]);

  // GSAP animations
  useEffect(() => {
    if (state?.userName) {
      // Animate main thank you message
      gsap.fromTo('.thankyou__main-message',
        { opacity: 0, y: 50 },
        { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out', delay: 0.2 }
      );

      // Animate social media post container
      gsap.fromTo('.thankyou__social-post',
        { opacity: 0, scale: 0.9, y: 30 },
        { opacity: 1, scale: 1, y: 0, duration: 0.6, ease: 'back.out(1.7)', delay: 0.5 }
      );

      // Animate social media icons
      gsap.fromTo('.thankyou__social-icon',
        { opacity: 0, scale: 0.8, y: 20 },
        { opacity: 1, scale: 1, y: 0, duration: 0.4, ease: 'back.out(1.7)', delay: 0.8, stagger: 0.1 }
      );

      // Animate back to home button
      gsap.fromTo('.thankyou__back-button',
        { opacity: 0, y: 20 },
        { opacity: 1, y: 0, duration: 0.5, ease: 'power2.out', delay: 1.2 }
      );
    }
  }, [state?.userName]);

  const handleBackToHome = () => {
    navigate('/');
  };

  const socialMediaLinks = [
    {
      name: 'Facebook',
      url: 'https://facebook.com/lawvriksh',
      color: '#1877F2',
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </svg>
      )
    },
    {
      name: 'Instagram',
      url: 'https://instagram.com/lawvriksh',
      color: '#E4405F',
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
        </svg>
      )
    },
    {
      name: 'LinkedIn',
      url: 'https://linkedin.com/company/lawvriksh',
      color: '#0A66C2',
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
        </svg>
      )
    },
    {
      name: 'X (Twitter)',
      url: 'https://twitter.com/lawvriksh',
      color: '#000000',
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
        </svg>
      )
    }
  ];

  if (!state?.userName) {
    return null; // Will redirect to home
  }

  return (
    <div className="thankyou">
      <div className="thankyou__container">
        {/* Main Thank You Message */}
        <div className="thankyou__main-message">
          <h1 className="thankyou__title">
            Thank you {state.userName}!!
          </h1>
          <p className="thankyou__subtitle">
            You have successfully enrolled in our program
          </p>
        </div>

        {/* Social Media Post Container */}
        <div className="thankyou__social-post">
          <div className="thankyou__post-header">
            <div className="thankyou__post-avatar">
              <img 
                src="/logo-dark.png" 
                alt="LawVriksh" 
                className="thankyou__avatar-image"
              />
            </div>
            <div className="thankyou__post-info">
              <h3 className="thankyou__post-username">LawVriksh</h3>
              <p className="thankyou__post-time">Just now</p>
            </div>
          </div>

          <div className="thankyou__post-content">
            <h2 className="thankyou__post-heading">
              ✨Congratulations for becoming our beta testing founding member!
              <br />
              Welcome aboard!
            </h2>
            
            <p className="thankyou__post-description">
              We're thrilled to have you join our growing community of legal professionals and enthusiasts. 
              By registering with LawVriksh, you've taken the first step towards unlocking a wealth of legal knowledge, 
              connecting with peers, and staying ahead in the ever-evolving legal landscape. We're committed to 
              providing you with valuable resources and opportunities to grow.
            </p>
          </div>

          {/* Social Media Icons */}
          <div className="thankyou__social-icons">
            {socialMediaLinks.map((social, index) => (
              <a
                key={social.name}
                href={social.url}
                target="_blank"
                rel="noopener noreferrer"
                className="thankyou__social-icon"
                style={{ '--social-color': social.color } as React.CSSProperties}
                title={`Follow us on ${social.name}`}
              >
                <span className="thankyou__social-svg">{social.icon}</span>
                <span className="thankyou__social-name">{social.name}</span>
              </a>
            ))}
          </div>
        </div>

        {/* Back to Home Button */}
        <div className="thankyou__back-button">
          <Button onClick={handleBackToHome} size="large">
            Back to Home
          </Button>
        </div>
      </div>
    </div>
  );
}
